const http = require('http');

/**
 * 测试 /focus API 接口
 * @param {number} port 服务器端口
 */
function testFocusApi(port) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: port,
            path: '/focus',
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log('✅ /focus API 测试成功:');
                    console.log(JSON.stringify(response, null, 2));
                    resolve(response);
                } catch (error) {
                    console.error('❌ 解析响应失败:', error);
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ 请求失败:', error);
            reject(error);
        });

        req.end();
    });
}

/**
 * 测试不存在的路由
 * @param {number} port 服务器端口
 */
function testNotFoundApi(port) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: port,
            path: '/nonexistent',
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log('✅ 404 测试成功:');
                    console.log(`状态码: ${res.statusCode}`);
                    console.log(JSON.stringify(response, null, 2));
                    resolve(response);
                } catch (error) {
                    console.error('❌ 解析响应失败:', error);
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ 请求失败:', error);
            reject(error);
        });

        req.end();
    });
}

// 如果直接运行此脚本，执行测试
if (require.main === module) {
    const port = process.argv[2] || 3001;
    
    console.log(`🧪 开始测试 HTTP 服务器 (端口: ${port})`);
    
    Promise.all([
        testFocusApi(port),
        testNotFoundApi(port)
    ]).then(() => {
        console.log('🎉 所有测试完成');
    }).catch((error) => {
        console.error('❌ 测试失败:', error);
        process.exit(1);
    });
}

module.exports = {
    testFocusApi,
    testNotFoundApi
};
