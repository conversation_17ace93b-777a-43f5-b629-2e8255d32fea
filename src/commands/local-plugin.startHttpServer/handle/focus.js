/**
 * Focus处理器
 * 执行local-plugin.focusAugment命令
 */

// 尝试加载vscode模块，如果失败则使用模拟对象
let vscode;
try {
    vscode = require('vscode');
} catch (error) {
    // 在独立测试环境中使用模拟对象
    vscode = {
        commands: {
            executeCommand: async (command) => {
                console.log(`模拟执行命令: ${command}`);
                return Promise.resolve();
            }
        }
    };
}

/**
 * 处理focus请求
 * @param {object} req HTTP请求对象
 * @param {object} res HTTP响应对象
 * @param {number} port 服务器端口
 */
async function handleFocus(req, res, port) {
    try {
        // 执行 local-plugin.focusAugment 命令
        await vscode.commands.executeCommand('local-plugin.focusAugment');
        await vscode.commands.executeCommand('workbench.action.focusSideBar');
        
        const responseData = {
            success: true,
            message: 'Focus Augment 命令执行成功',
            timestamp: new Date().toISOString(),
            command: 'local-plugin.focusAugment',
            port: port
        };
        
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(responseData, null, 2));
        
    } catch (error) {
        console.error('执行 Focus Augment 命令失败:', error);
        
        const errorData = {
            success: false,
            message: 'Focus Augment 命令执行失败',
            error: error.message,
            timestamp: new Date().toISOString(),
            command: 'local-plugin.focusAugment',
            port: port
        };
        
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(errorData, null, 2));
    }
}

module.exports = { handleFocus };
