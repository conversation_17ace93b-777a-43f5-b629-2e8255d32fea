/**
 * 404错误处理器
 */

/**
 * 处理404错误
 * @param {object} req HTTP请求对象
 * @param {object} res HTTP响应对象
 * @param {number} port 服务器端口
 */
function handleNotFound(req, res, port) {
    const errorData = {
        error: 'Not Found',
        message: `路径 ${req.url} 不存在`,
        timestamp: new Date().toISOString(),
        request: {
            method: req.method,
            url: req.url,
            headers: req.headers
        },
        server: {
            port: port,
            uptime: process.uptime()
        },
        availableEndpoints: [
            { method: 'GET', path: '/focus', description: '执行 Focus Augment 命令' }
        ],
        suggestions: getSuggestions(req.url)
    };
    
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(errorData, null, 2));
}

/**
 * 根据请求路径提供建议
 * @param {string} url 请求的URL
 * @returns {string[]} 建议列表
 */
function getSuggestions(url) {
    const suggestions = [];

    // 简单的路径匹配建议
    if (url.includes('focus')) {
        suggestions.push('尝试访问 /focus');
    }

    // 如果没有特定建议，提供通用建议
    if (suggestions.length === 0) {
        suggestions.push('尝试访问 /focus 执行 Focus Augment 命令');
    }

    return suggestions;
}

module.exports = { handleNotFound };
